import 'app_logger.dart';
import 'user_storage.dart';

/// 用户上下文管理器 - 简化版本
class UserContext {
  static UserContext? _instance;
  String? _currentUserUid;

  UserContext._();

  /// 获取单例实例
  static UserContext get instance {
    _instance ??= UserContext._();
    return _instance!;
  }

  /// 获取当前用户UID
  String? get currentUserUid => _currentUserUid;

  /// 检查是否有用户上下文
  bool get hasUserContext =>
      _currentUserUid != null && _currentUserUid!.isNotEmpty;

  /// 设置当前用户
  void setCurrentUser(String? userUid) {
    _currentUserUid = userUid;
    AppLogger.info('用户上下文已更新: ${userUid ?? "已清除"}');
  }

  /// 生成用户相关的存储键名
  String getUserKey(String baseKey) {
    if (_currentUserUid == null) {
      throw Exception('用户上下文未设置，无法生成存储键');
    }
    return UserStorage.getUserKey(baseKey, _currentUserUid!);
  }

  /// 清理当前用户数据
  Future<void> clearCurrentUserData() async {
    if (!hasUserContext) return;

    try {
      await UserStorage.clearUserData(_currentUserUid!);
      AppLogger.info('当前用户数据已清理: $_currentUserUid');
    } catch (e, stackTrace) {
      AppLogger.error('清理当前用户数据失败: $e', error: e, stackTrace: stackTrace);
    }
  }

  /// 切换用户
  Future<void> switchUser(String newUserUid,
      {bool clearCurrentData = false}) async {
    final oldUserUid = _currentUserUid;

    try {
      // 简单地切换用户上下文，每个用户的数据完全隔离
      setCurrentUser(newUserUid);
      AppLogger.info('用户切换完成: ${oldUserUid ?? "无"} -> $newUserUid');
    } catch (e, stackTrace) {
      AppLogger.error('用户切换失败: $e', error: e, stackTrace: stackTrace);
      _currentUserUid = oldUserUid;
      rethrow;
    }
  }

  /// 切换用户并通知 Provider 重新加载数据
  Future<void> switchUserAndReload(String newUserUid) async {
    try {
      await switchUser(newUserUid);

      // 通知所有相关的 Provider 重新加载数据
      // 这里可以使用事件总线或者其他机制来通知 Provider
      AppLogger.info('用户切换成功，请重新加载相关数据');
    } catch (e, stackTrace) {
      AppLogger.error('用户切换失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 检查当前用户是否有数据
  Future<bool> hasCurrentUserData() async {
    if (!hasUserContext) return false;
    return await UserStorage.hasUserData(_currentUserUid!);
  }

  /// 重置（用于测试）
  void reset() {
    _currentUserUid = null;
    AppLogger.info('用户上下文已重置');
  }

  /// 检查是否存在多个用户账户
  Future<bool> hasMultipleUsers() async {
    return await UserStorage.hasMultipleUsers();
  }

  /// 获取所有用户列表
  Future<List<String>> getAllUserUids() async {
    return await UserStorage.getAllUserUids();
  }

  /// 获取当前用户数据状态
  Future<Map<String, bool>> getCurrentUserDataStatus() async {
    if (!hasUserContext) return {};
    return await UserStorage.getUserDataStatus(_currentUserUid!);
  }

  /// 复制当前用户数据到另一个用户
  Future<void> copyCurrentUserDataTo(String targetUserUid) async {
    if (!hasUserContext) return;

    try {
      await UserStorage.copyUserData(_currentUserUid!, targetUserUid);
      AppLogger.info('当前用户数据已复制到: $targetUserUid');
    } catch (e, stackTrace) {
      AppLogger.error('复制用户数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }
}
