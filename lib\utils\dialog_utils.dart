import 'package:flutter/material.dart';
import '../constants/constants.dart';

/// 全局对话框工具类
/// 提供在应用任何位置显示对话框的能力
class DialogUtils {
  /// 显示警告确认对话框
  /// 
  /// [title] 对话框标题
  /// [content] 对话框内容
  /// [cancelText] 取消按钮文本，默认为"取消"
  /// [confirmText] 确认按钮文本，默认为"确定"
  /// [onConfirm] 确认按钮回调函数
  /// [onCancel] 取消按钮回调函数
  /// 
  /// 返回值：true表示用户点击确认，false表示用户点击取消或关闭对话框
  static Future<bool> showWarningDialog({
    required String title,
    required String content,
    String cancelText = '取消',
    String confirmText = '确定',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) async {
    final context = navigatorKey.currentContext;
    if (context == null) return false;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // 点击外部不关闭对话框
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(false);
                onCancel?.call();
              },
              child: Text(
                cancelText,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(true);
                onConfirm?.call();
              },
              child: Text(
                confirmText,
                style: TextStyle(
                  color: Colors.red[400],
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// 显示结果通知对话框
  /// 
  /// [title] 对话框标题
  /// [content] 对话框内容
  /// [isSuccess] 是否为成功状态，影响标题颜色
  /// [buttonText] 按钮文本，默认为"确定"
  /// [onConfirm] 确认按钮回调函数
  static Future<void> showResultDialog({
    required String title,
    required String content,
    bool isSuccess = true,
    String buttonText = '确定',
    VoidCallback? onConfirm,
  }) async {
    final context = navigatorKey.currentContext;
    if (context == null) return;

    await showDialog(
      context: context,
      barrierDismissible: false, // 点击外部不关闭对话框
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isSuccess ? Colors.green[600] : Colors.red[600],
            ),
          ),
          content: Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                onConfirm?.call();
              },
              child: Text(
                buttonText,
                style: TextStyle(
                  color: isSuccess ? Colors.green[600] : Colors.red[600],
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 显示加载对话框
  /// 
  /// [message] 加载提示信息
  /// 
  /// 返回一个可以用来关闭对话框的函数
  static VoidCallback showLoadingDialog({
    String message = '处理中...',
  }) {
    final context = navigatorKey.currentContext;
    if (context == null) return () {};

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(
                message,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        );
      },
    );

    // 返回关闭对话框的函数
    return () {
      final currentContext = navigatorKey.currentContext;
      if (currentContext != null) {
        Navigator.of(currentContext).pop();
      }
    };
  }
}
