import 'package:shared_preferences/shared_preferences.dart';
import 'app_logger.dart';
import '../constants/constants.dart';

/// 用户存储工具类 - 简化版本
class UserStorage {
  /// 生成用户相关的存储键名
  /// [baseKey] 基础键名
  /// [userUid] 用户UID
  static String getUserKey(String baseKey, String userUid) {
    return '${userUid}_$baseKey';
  }

  /// 清理指定用户的所有数据
  static Future<void> clearUserData(String userUid) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();

      for (String baseKey in AppConfig.userDataKeys) {
        final userKey = getUserKey(baseKey, userUid);
        await asyncPrefs.remove(userKey);
      }

      AppLogger.info(
          '用户数据已清理: $userUid (${AppConfig.userDataKeys.length}个数据类型)');
    } catch (e, stackTrace) {
      AppLogger.error('清理用户数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 检查用户是否有数据
  static Future<bool> hasUserData(String userUid) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();

      for (String baseKey in AppConfig.userDataKeys) {
        final userKey = getUserKey(baseKey, userUid);
        final data = await asyncPrefs.getString(userKey);
        if (data != null) return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// 获取用户数据统计信息
  static Future<Map<String, bool>> getUserDataStatus(String userUid) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();
      final status = <String, bool>{};

      for (String baseKey in AppConfig.userDataKeys) {
        final userKey = getUserKey(baseKey, userUid);
        final data = await asyncPrefs.getString(userKey);
        status[baseKey] = data != null;
      }

      return status;
    } catch (e, stackTrace) {
      AppLogger.error('获取用户数据状态失败: $e', error: e, stackTrace: stackTrace);
      return {};
    }
  }

  /// 复制用户数据到另一个用户（用于数据备份或迁移）
  static Future<void> copyUserData(String fromUserUid, String toUserUid) async {
    try {
      final asyncPrefs = SharedPreferencesAsync();

      for (String baseKey in AppConfig.userDataKeys) {
        final fromKey = getUserKey(baseKey, fromUserUid);
        final toKey = getUserKey(baseKey, toUserUid);

        final data = await asyncPrefs.getString(fromKey);
        if (data != null) {
          await asyncPrefs.setString(toKey, data);
        }
      }

      AppLogger.info('用户数据已复制: $fromUserUid -> $toUserUid');
    } catch (e, stackTrace) {
      AppLogger.error('复制用户数据失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 获取所有用户的UID列表
  static Future<List<String>> getAllUserUids() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();
      final userUids = <String>{};

      for (String key in allKeys) {
        if (key.contains('_') &&
            AppConfig.userDataKeys
                .any((dataKey) => key.endsWith('_$dataKey'))) {
          final uid = key.substring(0, key.lastIndexOf('_'));
          userUids.add(uid);
        }
      }

      return userUids.toList();
    } catch (e, stackTrace) {
      AppLogger.error('获取用户列表失败: $e', error: e, stackTrace: stackTrace);
      return [];
    }
  }

  /// 检查是否存在多个用户
  static Future<bool> hasMultipleUsers() async {
    final users = await getAllUserUids();
    return users.length > 1;
  }
}
