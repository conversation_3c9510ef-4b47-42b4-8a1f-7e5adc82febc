import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';

class User {
  final String uid;
  final String phone;
  final String accessToken;
  final String refreshToken;
  final String? memberShipLevel;
  final DateTime? memberShipExpireTime;
  final bool isLogin;

  User({
    required this.uid,
    required this.phone,
    required this.accessToken,
    required this.refreshToken,
    required this.isLogin,
    this.memberShipLevel,
    this.memberShipExpireTime,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      uid: json['uid'],
      phone: json['phone'],
      accessToken: json['accessToken'],
      refreshToken: json['refreshToken'],
      isLogin: json['isLogin'],
      memberShipLevel: json['memberShipLevel'],
      memberShipExpireTime: json['memberShipExpireTime'] == null
          ? null
          : DateTime.parse(json['memberShipExpireTime']),
    );
  }

  Map<String, dynamic> toJson() => {
        'uid': uid,
        'phone': phone,
        'accessToken': accessToken,
        'refreshToken': refreshToken,
        'isLogin': isLogin,
        'memberShipLevel': memberShipLevel,
        'memberShipExpireTime': memberShipExpireTime?.toIso8601String(),
      };

  static Future<User?> getUser() async {
    return await getCurrentUser();
  }

  static Future<User?> getCurrentUser() async {
    final storage = FlutterSecureStorage();
    final currentUserUid = await storage.read(key: 'current_user_uid');
    if (currentUserUid != null) {
      return await getUserByUid(currentUserUid);
    }
    return null;
  }

  static Future<User?> getUserByUid(String uid) async {
    final storage = FlutterSecureStorage();
    final userJson = await storage.read(key: 'user_$uid');
    if (userJson != null) {
      return User.fromJson(jsonDecode(userJson));
    }
    return null;
  }

  static Future<void> saveUser(User user) async {
    final storage = FlutterSecureStorage();

    // 保存用户数据（使用 uid 作为键）
    await storage.write(
        key: 'user_${user.uid}', value: jsonEncode(user.toJson()));
    // 设置当前用户（使用 uid）
    await storage.write(key: 'current_user_uid', value: user.uid);
  }

  static Future<void> clearUser() async {
    final storage = FlutterSecureStorage();

    // 获取当前用户UID
    final currentUserUid = await storage.read(key: 'current_user_uid');
    if (currentUserUid != null) {
      // 删除当前用户数据
      await storage.delete(key: 'user_$currentUserUid');
    }

    // 清除当前用户标记
    await storage.delete(key: 'current_user_uid');
  }
}
