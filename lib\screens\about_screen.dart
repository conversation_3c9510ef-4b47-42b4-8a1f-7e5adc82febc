import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import '../constants/constants.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.orange[30],
      appBar: AppBar(
        title: const Text('关于'),
        elevation: 0,
      ),
      body: Center(
        child: Column(
          // mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 60),
            // APP图标
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Image.asset(
                  'assets/images/logo.png',
                  fit: BoxFit.cover,
                ),
              ),
            ),

            const SizedBox(height: 5),

            // 应用名称
            Text(
              '智心伴侣',
              style: TextStyle(
                fontSize: 18,
              ),
            ),

            const SizedBox(height: 3),

            // 版本信息
            Text(
              '版本：${AppConfig.appVersion}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),

            SizedBox(height: 500),

            // 用户协议和隐私政策链接
            Text.rich(
              TextSpan(
                text: '智心伴侣',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.black,
                ),
                children: [
                  TextSpan(
                    text: '用户协议',
                    style: TextStyle(
                      color: Colors.orange.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Navigator.of(context).pushNamed(Routes.userAgreement);
                      },
                  ),
                  const TextSpan(
                      text: '和关于智心伴侣',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.black,
                      )),
                  TextSpan(
                    text: '隐私政策',
                    style: TextStyle(
                      color: Colors.orange.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Navigator.of(context).pushNamed(Routes.privacyPolicy);
                      },
                  ),
                  const TextSpan(
                      text: '的声明',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.black,
                      )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinkButton(
    BuildContext context,
    String text,
    VoidCallback onTap,
    ThemeData theme,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: theme.colorScheme.primary.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              text,
              style: TextStyle(
                fontSize: 16,
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: theme.colorScheme.primary,
            ),
          ],
        ),
      ),
    );
  }
}
