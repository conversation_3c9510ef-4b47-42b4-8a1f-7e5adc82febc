import 'dart:async';
import 'package:flutter/material.dart';
import '../models/device.dart';
import '../services/device_service.dart';
import '../utils/app_logger.dart';

class DeviceProvider with ChangeNotifier {
  // 依赖注入
  final DeviceApiService _deviceService;
  final DeviceControlService _deviceControlService;

  DeviceProvider(this._deviceService, this._deviceControlService);

  // 创建设备模型
  Device? _device;
  Device? get device => _device;

  // 获取防丢模式状态
  bool get isAntiLostModeEnabled => _device?.antiLostMode ?? false;

  // 超声驱虫状态管理
  bool _isRepellentActive = false;
  DateTime? _repellentStartTime;
  Timer? _repellentTimer;

  bool get isRepellentActive => _isRepellentActive;
  DateTime? get repellentStartTime => _repellentStartTime;

  // 初始化设备，从本地存储加载设备信息
  Future<void> initDevices(bool isLogin) async {
    try {
      _device = await Device.getDevice();
      if (_device == null && isLogin) {
        // 同步设备信息
        final deviceSyncInfo = await _deviceService.deviceSyncInfo();
        if (deviceSyncInfo.isNotEmpty) {
          createDevice(deviceSyncInfo);
        }
      } else if (_device != null && isLogin) {
        updateDevice({'isOnline': true});
      }
    } catch (e, s) {
      AppLogger.warning('初始化设备不成功: $e', error: e, stackTrace: s);
    }
  }

  // 创建设备
  Future<void> createDevice(Map<String, dynamic> data) async {
    _device = Device.fromJson(data);
    await Device.saveDevice(_device!);
    notifyListeners();
  }

  // 删除设备
  Future<void> removeDevice() async {
    _device = null;
    await Device.removeDevice();
    notifyListeners();
  }

  // 查询设备运行状态
  Future<void> updateRunningStatus() async {
    final deviceName = _device?.deviceName;
    if (deviceName == null) {
      return;
    }
    final data = await _deviceService.deviceRunningStatus(deviceName);
    if (data.isNotEmpty) {
      updateDevice(data);
    }
  }

  // 更新设备设备属性
  Future<void> updateDevice(Map<String, dynamic> data) async {
    if (_device == null) {
      return;
    }

    Map<String, dynamic> merged = {..._device!.toJson(), ...data};
    _device = Device.fromJson(merged);
    await Device.saveDevice(_device!);
    notifyListeners();
  }

  // 设置遛狗防丢模式状态
  Future<bool> setAntiLostMode(bool value) async {
    if (_device == null) {
      throw Exception('设备不存在，无法设置遛狗防丢模式');
    }

    try {
      // 调用API设置防丢模式
      final success = await _deviceControlService.antiLostMode(
        deviceName: _device!.deviceName,
        command: value ? 'on' : 'off',
      );

      // 如果API调用成功，更新设备状态并保存
      if (success) {
        updateDevice({'antiLostMode': value});
      }
      return success;
    } catch (e) {
      print('设置遛狗防丢模式失败: $e');
      return false;
    }
  }

  // 启动超声驱虫
  Future<bool> startRepellent() async {
    if (_device == null) {
      return false;
    }

    // 检查是否已经在运行
    if (_isRepellentActive && _repellentStartTime != null) {
      final now = DateTime.now();
      final difference = now.difference(_repellentStartTime!);
      if (difference.inMinutes < 5) {
        return true; // 已经在运行中
      }
    }

    // 调用API启动驱虫
    final success = await _deviceControlService.ultrasonicRepellent(
      deviceName: _device!.deviceName,
      command: 'on',
    );

    if (success) {
      _isRepellentActive = true;
      _repellentStartTime = DateTime.now();

      // 设置5分钟定时器自动停止
      _repellentTimer?.cancel();
      _repellentTimer = Timer(const Duration(minutes: 5), () {
        stopRepellent();
      });

      notifyListeners();
    }

    return success;
  }

  // 停止超声驱虫
  Future<bool> stopRepellent() async {
    if (_device == null) {
      return false;
    }

    // 调用API停止驱虫
    final success = await _deviceControlService.ultrasonicRepellent(
      deviceName: _device!.deviceName,
      command: 'off',
    );

    // 清除状态
    _isRepellentActive = false;
    _repellentStartTime = null;
    _repellentTimer?.cancel();
    _repellentTimer = null;

    notifyListeners();
    return success;
  }

  // // 获取设备wifi连接状态
  // void deviceWifiState(Device petDevice) async {
  //   try {
  //     final data =
  //         await _deviceService.deviceSleepOrOffline(petDevice.deviceName);
  //     updateDevice(data);
  //   } catch (e) {
  //     print('获取设备wifi连接状态失败: $e');
  //   }
  // }

  // 注册设备
  Future<Map<String, dynamic>> registerDevice() async {
    final deviceName = _device?.deviceName;
    final bleMac = _device?.bleMac;
    if (deviceName == null || bleMac == null) {
      return {
        'Success': false,
        'errorCode': 'notdevice',
        'errorMessage': '设备不存在！',
      };
    }
    // 上物联网云平台上检查设备激活情况
    final deviceInfoResponse = await _deviceService.deviceInfo(deviceName);

    if (deviceInfoResponse['Success'] == true) {
      // 如果设备已经激活，则在服务器上绑定用户
      final body = _device!.toJson();
      final bindResponse = await _deviceService.deviceBindUser(body);
      if (bindResponse['Success'] == true) {
        // 更新设备productId
        updateDevice({'productId': bindResponse['productId']});
        return bindResponse;
      } else {
        return bindResponse;
      }
    } else {
      notifyListeners();
      return deviceInfoResponse;
    }
  }

  // 解除项圈绑定
  Future<Map<String, dynamic>> unbindCollar() async {
    if (_device == null) {
      return {
        'success': false,
        'message': '未找到绑定的项圈设备',
      };
    }

    try {
      final deviceName = _device!.deviceName;
      // 检查设备是否在线
      final deviceStatus = await _deviceService.deviceRunningStatus(deviceName);
      if (!deviceStatus['isOnline']) {
        return {
          'success': false,
          'message': '项圈不在线，请检查项圈是否开机',
        };
      }
      // 开启跑马灯
      bool marqueeLightSuccess = await _deviceControlService.marqueeLight(
        deviceName: deviceName,
        command: 'on',
      );
      if (!marqueeLightSuccess) {
        return {
          'success': false,
          'message': '设备控制失败，请稍后重试',
        };
      }
      // 清除WIFI
      bool wifiConfigSuccess =
          await _deviceService.deviceWifiConfigUpdate(deviceName);
      if (!wifiConfigSuccess) {
        return {
          'success': false,
          'message': '清除项圈WIFI失败，请稍后重试',
        };
      }
      // 调用API解绑设备
      final success = await _deviceService.deviceUnbindUser(deviceName);
      if (success) {
        // 删除本地设备数据
        await removeDevice();

        AppLogger.info('项圈解绑成功: $deviceName');
        return {
          'success': true,
          'message': '解除绑定成功',
        };
      } else {
        return {
          'success': false,
          'message': '解绑失败，请稍后重试',
        };
      }
    } catch (e, stackTrace) {
      AppLogger.error('项圈解绑失败: $e', error: e, stackTrace: stackTrace);
      return {
        'success': false,
        'message': '解绑失败: ${e.toString()}',
      };
    }
  }
}
