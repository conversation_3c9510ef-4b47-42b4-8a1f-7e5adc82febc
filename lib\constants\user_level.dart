/// 用户等级枚举
/// 定义用户的会员等级类型
enum UserLevel {
  /// 普通用户
  normal('normal', '普通用户'),

  /// 黄金会员
  gold('gold', '黄金会员'),

  /// 铂金会员
  platinum('platinum', '铂金会员'),

  /// 钻石会员
  diamond('diamond', '钻石会员');

  const UserLevel(this.value, this.displayName);

  /// 等级值
  final String value;

  /// 显示名称
  final String displayName;

  /// 从字符串获取用户等级
  static UserLevel fromString(String? value) {
    if (value == null) return UserLevel.normal;

    for (UserLevel level in UserLevel.values) {
      if (level.value == value || level.displayName == value) {
        return level;
      }
    }
    return UserLevel.normal; // 默认值
  }

  /// 获取等级描述文本
  String getDescription(DateTime? expireTime) {
    final expireTimeStr = expireTime != null
        ? '${expireTime.year}年${expireTime.month.toString().padLeft(2, '0')}月${expireTime.day.toString().padLeft(2, '0')}日'
        : '未发现';

    switch (this) {
      case UserLevel.normal:
        return '普通用户自注册起享有7天高级会员，享有爱宠小博士、实时健康监测、AI智能分析健康报告。您体验到期时间-$expireTimeStr。';
      case UserLevel.gold:
        return '享有爱宠小博士、实时健康监测、AI智能分析健康报告；您会员到期时间-$expireTimeStr';
      case UserLevel.platinum:
        return '享有爱宠小博士、实时健康监测、AI智能分析健康报告；您会员到期时间-$expireTimeStr';
      case UserLevel.diamond:
        return '享有爱宠小博士、实时健康监测、AI智能分析健康报告；您会员到期时间-$expireTimeStr';
    }
  }

  /// 获取升级/续费按钮文本
  String get buttonText {
    switch (this) {
      case UserLevel.normal:
        return '立即升级';
      case UserLevel.gold:
      case UserLevel.platinum:
      case UserLevel.diamond:
        return '立即续费';
    }
  }
}
