import 'package:flutter/material.dart';

import 'package:jwt_decoder/jwt_decoder.dart';
import '../services/auth_service.dart';
import '../models/user.dart';
import '../models/pet.dart';
import '../utils/user_context.dart';
import '../utils/app_logger.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService;
  AuthProvider(this._authService);

  User? _user;
  User? get user => _user;
  bool get isLogin => _user?.isLogin ?? false;

  // 宠物相关状态
  Pet? _currentPet;
  bool _isPetLoading = false;

  // 宠物状态的 getter
  Pet? get currentPet => _currentPet;
  bool get isPetLoading => _isPetLoading;
  bool get hasPet => _currentPet != null;

  Future<void> init() async {
    _user = await User.getUser();
    if (_user != null) {
      // 设置用户上下文
      UserContext.instance.setCurrentUser(_user!.uid);

      updateUser({
        'isLogin': false,
      });
      final isExpired = JwtDecoder.isExpired(_user!.refreshToken);
      if (!isExpired) {
        await refreshToken();
      }
    }
  }

  Future<void> updateUser(Map<String, dynamic> data) async {
    if (_user == null) {
      throw Exception('用户不存在');
    }
    Map<String, dynamic> merged = {..._user!.toJson(), ...data};
    _user = User.fromJson(merged);
    await User.saveUser(_user!);
    notifyListeners();
    //加载宠物信息
    // await loadPetInfo();
  }

  Future<void> refreshToken() async {
    final data =
        await _authService.refreshToken(_user!.phone, _user!.refreshToken);
    updateUser(data);
  }

  Future<void> getSmsCode(String phone) async {
    try {
      await _authService.getSmsCode(phone);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> login(String phone, String smsCode) async {
    try {
      final data = await _authService.login(phone, smsCode);
      data['phone'] = phone;
      _user = User.fromJson(data);
      await User.saveUser(_user!);

      // 设置用户上下文，实现数据隔离
      UserContext.instance.setCurrentUser(_user!.uid);
      notifyListeners();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> changePhone(String newPhone, String smsCode) async {
    try {
      await _authService.changePhone(newPhone, smsCode);
      final data = _user!.toJson();
      data['phone'] = newPhone;
      await User.saveUser(User.fromJson(data));
      notifyListeners();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      await clearUser();
      notifyListeners();
      await _authService.logout();
    } catch (e) {
      await _authService.logout();
    }
  }

  Future<void> clearUser() async {
    if (_user != null) {
      // 清理用户相关的本地数据
      try {
        await UserContext.instance.clearCurrentUserData();
      } catch (e) {
        // 清理失败不应该阻止用户退出登录，只记录错误
        AppLogger.error('清理用户数据失败: $e', error: e);
      }

      // 清理宠物数据
      clearPetData();

      // 清除用户上下文
      UserContext.instance.setCurrentUser(null);

      _user = null;
      await User.clearUser();
      notifyListeners();
    }
  }

  // ==================== 宠物管理方法 ====================

  /// 加载宠物信息
  Future<void> loadPetInfo() async {
    if (!isLogin) return;

    try {
      _isPetLoading = true;
      notifyListeners();

      Pet? pet = await Pet.getPet();
      if (pet == null) {
        final petInfo = await _authService.petInfo();
        if (petInfo.isNotEmpty) {
          pet = Pet.fromJson(petInfo);
          await Pet.savePet(pet);
        }
      }
      _currentPet = pet;

      AppLogger.info('宠物信息加载完成: ${pet?.nickname ?? "无宠物"}');
    } catch (e, stackTrace) {
      AppLogger.error('加载宠物信息失败: $e', error: e, stackTrace: stackTrace);
      _currentPet = null;
    } finally {
      _isPetLoading = false;
      notifyListeners();
    }
  }

  /// 添加或更新宠物信息
  Future<void> savePet(Pet pet) async {
    if (!isLogin) {
      throw Exception('用户未登录');
    }

    try {
      _isPetLoading = true;
      notifyListeners();

      // 调用 API 保存宠物信息
      final result = await _authService.petUpsert(pet);

      // 更新本地宠物信息，包含服务器返回的 ID
      final updatedPet = pet.copyWith(id: result['pet_id'].toInt());
      await Pet.savePet(updatedPet);

      _currentPet = updatedPet;
      AppLogger.info('宠物信息保存成功: ${updatedPet.nickname}');
    } catch (e, stackTrace) {
      AppLogger.error('保存宠物信息失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      _isPetLoading = false;
      notifyListeners();
    }
  }

  /// 删除宠物信息
  Future<void> deletePet() async {
    if (!isLogin || _currentPet == null) {
      throw Exception('用户未登录或无宠物信息');
    }

    try {
      _isPetLoading = true;
      notifyListeners();

      // 调用 API 删除宠物信息
      await _authService.petDelete(_currentPet!);

      // 删除本地宠物信息
      await Pet.deletePet();

      _currentPet = null;
      AppLogger.info('宠物信息删除成功');
    } catch (e, stackTrace) {
      AppLogger.error('删除宠物信息失败: $e', error: e, stackTrace: stackTrace);
      rethrow;
    } finally {
      _isPetLoading = false;
      notifyListeners();
    }
  }

  /// 清理宠物数据（内部方法，用于登出时清理）
  void clearPetData() {
    _currentPet = null;
    _isPetLoading = false;
    AppLogger.info('宠物数据已清理');
  }

  /// 刷新宠物信息（用于页面返回时刷新）
  Future<void> refreshPetInfo() async {
    if (_currentPet != null) return;
    await loadPetInfo();
  }
}
